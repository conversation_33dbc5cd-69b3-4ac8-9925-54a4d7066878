"""
6台空调联合预测模型
实现空调间相互影响的机器学习预测算法
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class AirConditionerJointPredictor:
    """6台空调联合预测器"""
    
    def __init__(self, data_precision=np.float16, device='cpu'):
        self.data_precision = data_precision
        self.device = device
        self.num_ac = 6  # 6台空调
        self.num_sensors = 48  # 48个温湿度传感器
        
        # 数据标准化器
        self.input_scaler = StandardScaler()
        self.output_scaler = StandardScaler()
        
        # 模型组件
        self.neural_network = None
        self.weight_learner = WeightLearner(self.num_sensors, self.num_ac)
        self.is_trained = False
        
        # 历史数据存储
        self.history_buffer = []
        self.max_history = 100  # 保存最近100条记录用于在线学习
        
        # 初始化权重矩阵 - 48个传感器对6台空调的影响权重
        self._initialize_weights()
        
    def _initialize_weights(self):
        """初始化权重矩阵：48个传感器对6台空调的影响权重"""
        # 初始权重设为相等，每个传感器对每台空调的影响权重为1/48
        self.weight_matrix = np.ones((self.num_sensors, self.num_ac), dtype=self.data_precision) / self.num_sensors
        
    def _create_feature_matrix(self, processed_data: Dict) -> np.ndarray:
        """创建特征矩阵"""
        features = []
        
        # 1. 室内温度传感器数据 (48个)
        temp_sensors = np.array(processed_data['indoor_temperature_sensor'], dtype=self.data_precision)
        features.extend(temp_sensors)
        
        # 2. 室内湿度传感器数据 (48个)
        humidity_sensors = np.array(processed_data['indoor_humidity_sensor'], dtype=self.data_precision)
        features.extend(humidity_sensors)
        
        # 3. 空调设定温度 (6个)
        setting_temps = np.array(processed_data['air_conditioner_setting_temperature'], dtype=self.data_precision)
        features.extend(setting_temps)
        
        # 4. 空调设定湿度 (6个)
        setting_humidities = np.array(processed_data['air_conditioner_setting_humidity'], dtype=self.data_precision)
        features.extend(setting_humidities)
        
        # 5. 空调制冷模式 (处理艾特网能空调的双制冷系统)
        cooling_modes = self._process_cooling_modes(processed_data['air_conditioner_cooling_mode'])
        features.extend(cooling_modes)
        
        # 6. 加权温湿度特征 (使用权重矩阵)
        weighted_temp_features = self._create_weighted_features(temp_sensors, humidity_sensors)
        features.extend(weighted_temp_features)
        
        return np.array(features, dtype=self.data_precision)
        
    def _process_cooling_modes(self, cooling_mode_data: List) -> List:
        """处理制冷模式数据，考虑艾特网能空调的双制冷系统"""
        processed_modes = []
        
        # 根据uid_config.yaml，艾特网能空调有8个UID，其他空调各1个
        # 艾特网能空调1#: 取前2个UID的平均值
        if len(cooling_mode_data) >= 2:
            ac1_mode = (cooling_mode_data[0] + cooling_mode_data[1]) / 2
            processed_modes.append(ac1_mode)
        
        # 艾特网能空调2#: 取第3、4个UID的平均值  
        if len(cooling_mode_data) >= 4:
            ac2_mode = (cooling_mode_data[2] + cooling_mode_data[3]) / 2
            processed_modes.append(ac2_mode)
        
        # 其他4台空调：直接使用对应的UID
        for i in range(4, min(8, len(cooling_mode_data))):
            processed_modes.append(cooling_mode_data[i])
            
        # 确保有6个值
        while len(processed_modes) < 6:
            processed_modes.append(0.0)
            
        return processed_modes[:6]
        
    def _create_weighted_features(self, temp_sensors: np.ndarray, humidity_sensors: np.ndarray) -> List:
        """使用权重矩阵创建加权特征"""
        weighted_features = []
        
        # 为每台空调计算加权温湿度
        for i in range(self.num_ac):
            # 加权温度
            weighted_temp = np.sum(temp_sensors * self.weight_matrix[:, i])
            weighted_features.append(weighted_temp)
            
            # 加权湿度
            weighted_humidity = np.sum(humidity_sensors * self.weight_matrix[:, i])
            weighted_features.append(weighted_humidity)
            
        return weighted_features
        
    def _create_target_matrix(self, processed_data: Dict) -> np.ndarray:
        """创建目标矩阵（6台空调的能耗和室内温度）"""
        targets = []
        
        # 能耗目标 - 处理3个数据源到6台空调的映射
        energy_data = processed_data['energy_consumption_collection']
        energy_targets = self._map_energy_to_ac(energy_data)
        targets.extend(energy_targets)
        
        # 室内温度目标（使用加权平均）
        temp_sensors = np.array(processed_data['indoor_temperature_sensor'])
        for i in range(self.num_ac):
            weighted_temp = np.sum(temp_sensors * self.weight_matrix[:, i])
            targets.append(weighted_temp)
            
        return np.array(targets, dtype=self.data_precision)
        
    def _map_energy_to_ac(self, energy_data: List) -> List:
        """将3个能耗数据源映射到6台空调"""
        energy_targets = []
        
        if len(energy_data) >= 3:
            # 第1个数据源：艾特网能空调1#
            energy_targets.append(energy_data[0])
            
            # 第2个数据源：艾特网能空调2# + 美的空调，平均分配
            combined_energy = energy_data[1] / 2
            energy_targets.append(combined_energy)  # 艾特网能空调2#
            energy_targets.append(combined_energy)  # 美的空调
            
            # 第3个数据源：维谛精密空调1+2+3，平均分配
            vertiv_energy = energy_data[2] / 3
            energy_targets.extend([vertiv_energy] * 3)  # 3台维谛空调
        else:
            # 如果数据不足，用0填充
            energy_targets = [0.0] * 6
            
        return energy_targets
        
    def train(self, training_data: List[Dict], epochs: int = 100, learning_rate: float = 0.001):
        """训练联合预测模型"""
        logger.info("开始训练6台空调联合预测模型...")
        
        # 准备训练数据
        X_list, y_list = [], []
        
        for data_point in training_data:
            features = self._create_feature_matrix(data_point)
            targets = self._create_target_matrix(data_point)
            X_list.append(features)
            y_list.append(targets)
            
        X = np.array(X_list)
        y = np.array(y_list)
        
        # 数据标准化
        X_scaled = self.input_scaler.fit_transform(X)
        y_scaled = self.output_scaler.fit_transform(y)
        
        # 创建神经网络
        input_size = X_scaled.shape[1]
        self.neural_network = JointPredictionNeuralNetwork(input_size, num_ac=self.num_ac)
        
        # 训练模型
        self._train_neural_network(X_scaled, y_scaled, epochs, learning_rate)
        
        # 学习权重矩阵
        self._update_weights(training_data)
        
        self.is_trained = True
        logger.info("模型训练完成")
        
    def _train_neural_network(self, X: np.ndarray, y: np.ndarray, epochs: int, learning_rate: float):
        """训练神经网络"""
        X_tensor = torch.FloatTensor(X)
        y_tensor = torch.FloatTensor(y)
        
        optimizer = optim.Adam(self.neural_network.parameters(), lr=learning_rate)
        criterion = nn.MSELoss()
        
        for epoch in range(epochs):
            optimizer.zero_grad()
            outputs = self.neural_network(X_tensor)
            loss = criterion(outputs, y_tensor)
            loss.backward()
            optimizer.step()
            
            if (epoch + 1) % 20 == 0:
                logger.info(f"Epoch [{epoch+1}/{epochs}], Loss: {loss.item():.4f}")
                
    def _update_weights(self, training_data: List[Dict]):
        """更新传感器权重矩阵"""
        # 收集传感器数据和空调性能数据
        sensor_data = []
        ac_performance = []
        
        for data_point in training_data:
            # 传感器数据
            temp_sensors = data_point['indoor_temperature_sensor']
            humidity_sensors = data_point['indoor_humidity_sensor']
            sensors = temp_sensors + humidity_sensors
            sensor_data.append(sensors)
            
            # 空调性能数据（能耗）
            energy_targets = self._map_energy_to_ac(data_point['energy_consumption_collection'])
            ac_performance.append(energy_targets)
            
        sensor_data = np.array(sensor_data)
        ac_performance = np.array(ac_performance)
        
        # 使用权重学习器更新权重
        self.weight_matrix = self.weight_learner.learn_weights(sensor_data, ac_performance)
        
    def predict(self, input_data: Dict) -> Dict:
        """进行联合预测"""
        if not self.is_trained:
            raise RuntimeError("模型尚未训练，请先调用train()方法")
            
        # 创建特征矩阵
        features = self._create_feature_matrix(input_data)
        
        # 标准化
        features_scaled = self.input_scaler.transform(features.reshape(1, -1))
        
        # 预测
        with torch.no_grad():
            features_tensor = torch.FloatTensor(features_scaled)
            predictions_scaled = self.neural_network(features_tensor)
            predictions = self.output_scaler.inverse_transform(predictions_scaled.numpy())
            
        # 解析预测结果
        predictions = predictions[0]
        
        result = {
            'energy_consumption_collection': predictions[:6].tolist(),
            'indoor_temperature_sensor': predictions[6:12].tolist()
        }
        
        return result
        
    def update_weights(self, new_weight_matrix: np.ndarray):
        """手动更新权重矩阵"""
        if new_weight_matrix.shape != (self.num_sensors, self.num_ac):
            raise ValueError(f"权重矩阵形状应为({self.num_sensors}, {self.num_ac})")
            
        self.weight_matrix = new_weight_matrix.astype(self.data_precision)
        logger.info("权重矩阵已更新")
        
    def get_weights(self) -> np.ndarray:
        """获取当前权重矩阵"""
        return self.weight_matrix.copy()


class WeightLearner:
    """权重学习器：学习传感器对空调的影响权重"""
    
    def __init__(self, num_sensors: int, num_ac: int):
        self.num_sensors = num_sensors
        self.num_ac = num_ac
        self.weight_models = {}
        
        # 为每台空调创建一个随机森林模型
        for i in range(num_ac):
            self.weight_models[f'ac_{i}'] = RandomForestRegressor(
                n_estimators=100,
                random_state=42,
                n_jobs=-1
            )
            
    def learn_weights(self, sensor_data: np.ndarray, ac_performance: np.ndarray) -> np.ndarray:
        """
        学习传感器权重
        
        Args:
            sensor_data: [n_samples, num_sensors] 传感器数据
            ac_performance: [n_samples, num_ac] 空调性能数据
            
        Returns:
            weight_matrix: [num_sensors, num_ac] 权重矩阵
        """
        weight_matrix = np.zeros((self.num_sensors, self.num_ac))
        
        for i in range(self.num_ac):
            # 训练模型
            self.weight_models[f'ac_{i}'].fit(sensor_data, ac_performance[:, i])
            
            # 获取特征重要性作为权重
            importance = self.weight_models[f'ac_{i}'].feature_importances_
            
            # 归一化权重
            weight_matrix[:, i] = importance / np.sum(importance)
            
        return weight_matrix.astype(np.float16)


class JointPredictionNeuralNetwork(nn.Module):
    """联合预测神经网络"""
    
    def __init__(self, input_size: int, hidden_size: int = 128, num_ac: int = 6):
        super().__init__()
        self.num_ac = num_ac
        
        # 编码器：提取共享特征
        self.encoder = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
        )
        
        # 注意力机制：学习空调间的相互影响
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, batch_first=True)
        
        # 解码器：为每台空调生成预测
        self.decoder = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 2)  # 每台空调输出2个值：能耗和温度
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # 编码
        encoded = self.encoder(x)  # [batch_size, hidden_size]
        
        # 为每台空调复制编码特征
        ac_features = encoded.unsqueeze(1).repeat(1, self.num_ac, 1)  # [batch_size, num_ac, hidden_size]
        
        # 注意力机制学习空调间相互影响
        attended_features, _ = self.attention(ac_features, ac_features, ac_features)
        
        # 解码：为每台空调生成预测
        predictions = []
        for i in range(self.num_ac):
            ac_pred = self.decoder(attended_features[:, i, :])  # [batch_size, 2]
            predictions.append(ac_pred)
            
        # 拼接所有空调的预测结果
        output = torch.cat(predictions, dim=1)  # [batch_size, num_ac * 2]
        
        return output
